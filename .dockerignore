# Build artifacts
build/
*.o
*.a
*.so
*.exe
*.out
*.app

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Test results
test_results.xml
benchmark_results.json
performance_results/

# Debug files
debug_*
*.log

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/

# Package manager files
node_modules/
.npm/

# Coverage reports
coverage/
*.gcov
*.gcda
*.gcno

# Static analysis
cppcheck-report.xml
clang-tidy-report.txt
