name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  BUILD_TYPE: Release

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
        compiler: [gcc-12, clang-15]
        build_type: [Debug, Release]

    steps:
    - uses: actions/checkout@v4

    - name: Install dependencies (Ubuntu)
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake build-essential

        if [[ "${{ matrix.compiler }}" == "gcc-12" ]]; then
          sudo apt-get install -y gcc-12 g++-12
          echo "CC=gcc-12" >> $GITHUB_ENV
          echo "CXX=g++-12" >> $GITHUB_ENV
        elif [[ "${{ matrix.compiler }}" == "clang-15" ]]; then
          wget -qO- https://apt.llvm.org/llvm-snapshot.gpg.key | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/apt.llvm.org.gpg
          sudo add-apt-repository "deb http://apt.llvm.org/$(lsb_release -cs)/ llvm-toolchain-$(lsb_release -cs)-15 main"
          sudo apt-get update
          sudo apt-get install -y clang-15 libc++-15-dev libc++abi-15-dev
          echo "CC=clang-15" >> $GITHUB_ENV
          echo "CXX=clang++-15" >> $GITHUB_ENV
        fi

    - name: Configure CMake
      run: |
        cmake -B ${{github.workspace}}/build \
              -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              -DCMAKE_CXX_COMPILER=${{ env.CXX }} \
              -DCMAKE_C_COMPILER=${{ env.CC }}

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config ${{ matrix.build_type }} -j$(nproc)

    - name: Test
      working-directory: ${{github.workspace}}/build
      run: |
        ./tests/betterdns_tests --gtest_output=xml:test_results.xml

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.os }}-${{ matrix.compiler }}-${{ matrix.build_type }}
        path: ${{github.workspace}}/build/test_results.xml

  benchmark:
    runs-on: ubuntu-22.04
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake build-essential gcc-12 g++-12

    - name: Configure CMake
      run: |
        cmake -B ${{github.workspace}}/build \
              -DCMAKE_BUILD_TYPE=Release \
              -DCMAKE_CXX_COMPILER=g++-12 \
              -DCMAKE_C_COMPILER=gcc-12

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config Release -j$(nproc)

    - name: Run Benchmarks
      working-directory: ${{github.workspace}}/build
      run: |
        ./benchmarks/betterdns_benchmark --benchmark_out=benchmark_results.json --benchmark_out_format=json

    - name: Upload benchmark results
      uses: actions/upload-artifact@v4
      with:
        name: benchmark-results
        path: ${{github.workspace}}/build/benchmark_results.json

  static-analysis:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake build-essential gcc-12 g++-12 cppcheck
        wget -qO- https://apt.llvm.org/llvm-snapshot.gpg.key | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/apt.llvm.org.gpg
        sudo add-apt-repository "deb http://apt.llvm.org/$(lsb_release -cs)/ llvm-toolchain-$(lsb_release -cs)-15 main"
        sudo apt-get update
        sudo apt-get install -y clang-tidy-15

    - name: Configure CMake
      run: |
        cmake -B ${{github.workspace}}/build \
              -DCMAKE_BUILD_TYPE=Debug \
              -DCMAKE_CXX_COMPILER=g++-12 \
              -DCMAKE_C_COMPILER=gcc-12 \
              -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

    - name: Run cppcheck
      run: |
        cppcheck --enable=all --std=c++23 --suppress=missingIncludeSystem \
                 --xml --xml-version=2 src/ 2> cppcheck-report.xml || true

    - name: Run clang-tidy
      run: |
        find src/ -name "*.cpp" -exec clang-tidy-15 {} -p build/ \; > clang-tidy-report.txt 2>&1 || true

    - name: Upload static analysis results
      uses: actions/upload-artifact@v4
      with:
        name: static-analysis-results
        path: |
          cppcheck-report.xml
          clang-tidy-report.txt

  coverage:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake build-essential gcc-12 g++-12 lcov

    - name: Configure CMake with coverage
      run: |
        cmake -B ${{github.workspace}}/build \
              -DCMAKE_BUILD_TYPE=Debug \
              -DCMAKE_CXX_COMPILER=g++-12 \
              -DCMAKE_C_COMPILER=gcc-12 \
              -DCMAKE_CXX_FLAGS="--coverage -g -O0" \
              -DCMAKE_C_FLAGS="--coverage -g -O0"

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config Debug -j$(nproc)

    - name: Run tests
      working-directory: ${{github.workspace}}/build
      run: ./tests/betterdns_tests

    - name: Generate coverage report
      working-directory: ${{github.workspace}}/build
      run: |
        # Use gcov-12 to match compiler version
        lcov --gcov-tool gcov-12 --capture --directory . --output-file coverage.info
        lcov --remove coverage.info '/usr/*' '*/tests/*' '*/benchmarks/*' '*/googletest/*' '*/_deps/*' --output-file coverage.info
        genhtml coverage.info --output-directory coverage_html

    - name: Upload coverage to Codecov
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: codecov/codecov-action@v3
      with:
        file: ${{github.workspace}}/build/coverage.info
        token: ${{ secrets.CODECOV_TOKEN }}
        fail_ci_if_error: false

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: ${{github.workspace}}/build/coverage_html/
