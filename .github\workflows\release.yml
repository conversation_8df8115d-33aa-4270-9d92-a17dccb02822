name: Release

on:
  release:
    types: [published]

permissions:
  contents: write
  packages: write
  issues: read

jobs:
  build-and-upload:
    name: Build and Upload Release Assets
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            cmake \
            gcc-12 \
            g++-12 \
            clang-tidy \
            clang-format \
            valgrind \
            pkg-config

      - name: Configure CMake
        run: |
          mkdir build
          cd build
          cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_COMPILER=g++-12
        
      - name: Build project
        run: |
          cd build
          make -j$(nproc)
        
      - name: Run tests
        run: |
          cd build
          ctest --output-on-failure
        
      - name: Package binaries
        run: |
          cd build
          # Create release package directory
          mkdir -p betterdns-${{ github.event.release.tag_name }}
          
          # Copy files
          cp betterdns betterdns-${{ github.event.release.tag_name }}/
          cp ../README.md betterdns-${{ github.event.release.tag_name }}/
          cp ../LICENSE betterdns-${{ github.event.release.tag_name }}/
          cp ../CHANGELOG.md betterdns-${{ github.event.release.tag_name }}/
          
          # Strip binary to reduce size
          strip betterdns-${{ github.event.release.tag_name }}/betterdns
          
          # Create tarball
          tar -czf betterdns-${{ github.event.release.tag_name }}-linux-x86_64.tar.gz betterdns-${{ github.event.release.tag_name }}/
        
      - name: Upload release assets
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release upload ${{ github.event.release.tag_name }} \
            ./build/betterdns-${{ github.event.release.tag_name }}-linux-x86_64.tar.gz

  docker-release:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    if: github.event.release.prerelease == false  # Skip prereleases
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
        
      - name: Extract version from tag
        id: version
        run: echo "VERSION=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            uiuxarghya/betterdns:latest
            uiuxarghya/betterdns:${{ steps.version.outputs.VERSION }}
            ghcr.io/uiuxarghya/betterdns:latest
            ghcr.io/uiuxarghya/betterdns:${{ steps.version.outputs.VERSION }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=registry,ref=uiuxarghya/betterdns:buildcache
          cache-to: type=registry,ref=uiuxarghya/betterdns:buildcache,mode=max
