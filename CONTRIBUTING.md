# Contributing to BetterDNS

Thank you for your interest in contributing to BetterDNS! This document provides guidelines for contributing to the project.

## 📚 Documentation

For comprehensive contributing guidelines, development setup, and best practices, visit:

**🌐 [https://betterdns.arghya.dev/docs/contributing](https://betterdns.arghya.dev/docs/contributing)**

This file provides a quick reference. For detailed information, please refer to the official documentation website.

## Code of Conduct

By participating in this project, you agree to maintain a respectful and inclusive environment for all contributors.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally
3. Create a new branch for your feature or bug fix
4. Make your changes
5. Test your changes thoroughly
6. Submit a pull request

## Development Setup

### Prerequisites

- C++23 compatible compiler (GCC 12+ or Clang 15+)
- CMake 3.20 or higher
- Git
- GoogleTest (automatically downloaded by CMake)
- Google Benchmark (automatically downloaded by CMake)

### Building for Development

```bash
# Clone your fork
git clone https://github.com/YOUR_USERNAME/betterdns.git
cd betterdns

# Create debug build
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Build with all warnings and sanitizers
make -j$(nproc)

# Run tests
./tests/betterdns_tests

# Run benchmarks
./benchmarks/betterdns_benchmark
```

## Coding Standards

### C++ Style Guidelines

- Follow modern C++23 practices
- Use RAII for resource management
- Prefer smart pointers over raw pointers
- Use const-correctness throughout
- Follow the existing naming conventions:
  - Classes: `PascalCase`
  - Functions/methods: `snake_case`
  - Variables: `snake_case`
  - Constants: `UPPER_SNAKE_CASE`
  - Namespaces: `lowercase`

### Code Formatting

Use the provided formatting script:

```bash
# Format all source files
./scripts/format.sh

# Lint code for style issues
./scripts/lint.sh
```

### Documentation

- Document all public APIs with Doxygen comments
- Include usage examples for complex functions
- Update relevant documentation files when adding features
- Write clear commit messages

## Testing Requirements

### Unit Tests

- All new functionality must include unit tests
- Tests should cover both success and failure cases
- Use GoogleTest framework
- Aim for high code coverage

### Integration Tests

- Add integration tests for new DNS resolution features
- Test with real DNS queries when appropriate
- Include performance regression tests

### Running Tests

```bash
# Run all tests
cd build
./tests/betterdns_tests

# Run specific test suites
./tests/betterdns_tests --gtest_filter="ResolverTest.*"

# Run with verbose output
./tests/betterdns_tests --gtest_output=xml:test_results.xml
```

## Submitting Changes

### Pull Request Process

1. Ensure your code follows the coding standards
2. Add or update tests for your changes
3. Update documentation as needed
4. Ensure all tests pass
5. Create a clear pull request description

### Commit Message Format

Use conventional commit format:

```
type(scope): brief description

Detailed explanation of the changes made.

- List specific changes
- Include any breaking changes
- Reference related issues

Fixes #123
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Review Process

1. All pull requests require review
2. Address reviewer feedback promptly
3. Keep pull requests focused and reasonably sized
4. Rebase your branch if needed to maintain clean history

## Reporting Issues

### Bug Reports

Include:
- Operating system and version
- Compiler version
- Steps to reproduce
- Expected vs actual behavior
- Relevant logs or error messages

### Feature Requests

Include:
- Clear description of the proposed feature
- Use cases and motivation
- Potential implementation approach
- Any breaking changes

## Development Guidelines

### Performance Considerations

- Profile code changes for performance impact
- Use benchmarks to validate optimizations
- Consider memory usage and allocation patterns
- Test with various DNS query types and sizes

### Security Considerations

- Validate all input data
- Handle network errors gracefully
- Avoid buffer overflows and memory leaks
- Follow secure coding practices

### Compatibility

- Maintain compatibility with supported platforms
- Test on multiple compilers when possible
- Consider impact on existing APIs

## Getting Help

- Open an issue for questions
- Check existing documentation
- Review similar implementations in the codebase
- Ask for clarification in pull request discussions

## License

By contributing to BetterDNS, you agree that your contributions will be licensed under the BSD 3 Clause License.
