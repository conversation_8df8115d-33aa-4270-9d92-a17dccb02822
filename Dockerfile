# Multi-stage Docker build for BetterDNS
# Stage 1: Build environment
FROM ubuntu:22.04 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    gcc-12 \
    g++-12 \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Create build directory and configure
RUN rm -rf build && mkdir build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release \
             -DCMAKE_CXX_COMPILER=g++-12 \
             -DCMAKE_C_COMPILER=gcc-12

# Build the project
RUN cd build && make -j$(nproc)

# Run tests to ensure build is working
RUN cd build && ./tests/betterdns_tests

# Stage 2: Runtime environment
FROM ubuntu:22.04 AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -r -s /bin/false betterdns

# Copy built executable
COPY --from=builder /app/build/betterdns /usr/local/bin/betterdns

# Set permissions
RUN chmod +x /usr/local/bin/betterdns

# Switch to non-root user
USER betterdns

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/betterdns"]

# Default command (show help)
CMD ["--help"]

# Metadata
LABEL maintainer="Arghya Ghosh <<EMAIL>>"
LABEL description="BetterDNS - A production-ready recursive DNS resolver"
LABEL version="1.0.0"
LABEL documentation="https://betterdns.arghya.dev"
LABEL repository="https://github.com/uiuxarghya/betterdns"
