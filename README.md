# BetterDNS

```
██████╗ ███████╗████████╗████████╗███████╗██████╗     ██████╗ ███╗   ██╗███████╗
██╔══██╗██╔════╝╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗    ██╔══██╗████╗  ██║██╔════╝
██████╔╝█████╗     ██║      ██║   █████╗  ██████╔╝    ██║  ██║██╔██╗ ██║███████╗
██╔══██╗██╔══╝     ██║      ██║   ██╔══╝  ██╔══██╗    ██║  ██║██║╚██╗██║╚════██║
██████╔╝███████╗   ██║      ██║   ███████╗██║  ██║    ██████╔╝██║ ╚████║███████║
╚═════╝ ╚══════╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝    ╚═════╝ ╚═╝  ╚═══╝╚══════╝
```

[![Build Status](https://github.com/uiuxarghya/betterdns/workflows/CI/badge.svg)](https://github.com/uiuxarghya/betterdns/actions)
[![License: BSD-3-Clause](https://img.shields.io/github/license/uiuxarghya/betterdns)](https://github.com/uiuxarghya/betterdns/blob/main/LICENSE)
[![C++23](https://img.shields.io/badge/C%2B%2B-23-blue.svg)](https://en.cppreference.com/w/cpp/23)
[![Documentation](https://img.shields.io/badge/docs-betterdns.arghya.dev-blue)](https://betterdns.arghya.dev)
[![Docker](https://img.shields.io/badge/docker-available-blue/?icon=docker)](https://github.com/uiuxarghya/betterdns/pkgs/container/betterdns)

**A production-ready recursive DNS resolver implemented in C++23 from scratch**


## 📚 Documentation

For comprehensive documentation, tutorials, API reference, and advanced usage examples, visit:

**🌐 [https://betterdns.arghya.dev](https://betterdns.arghya.dev)**

This README provides a quick start guide. For detailed information, please refer to the official documentation website.

## 🚀 Quick Start

```bash
# Clone and build
git clone https://github.com/uiuxarghya/betterdns.git
cd betterdns && mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release && make -j$(nproc)

# Basic usage
./betterdns example.com
./betterdns -v -t AAAA example.com

# Docker usage
docker run --rm ghcr.io/uiuxarghya/betterdns:latest example.com
```

## Overview

BetterDNS is a complete DNS resolver that implements the full recursive resolution process by directly querying root servers, TLD servers, and authoritative servers without using OS-provided resolver functions. It follows RFC standards and provides high performance with intelligent caching.

> 📖 **For detailed documentation, tutorials, and API reference, visit [betterdns.arghya.dev](https://betterdns.arghya.dev)**

## ✨ Features

- **🔄 Full Recursive Resolution**: Manually implements the complete DNS resolution chain from root servers to authoritative servers
- **📋 RFC Compliance**: Strictly follows DNS protocol specifications (RFC 1035, 1034, 2181, 4034, 7766)
- **⚡ Modern C++23**: Uses latest C++ features with proper RAII and memory management
- **🚀 High Performance**: Intelligent caching with TTL management and LRU eviction
- **🔒 Thread Safety**: Concurrent DNS queries with proper synchronization
- **🧪 Comprehensive Testing**: Unit tests, integration tests, and benchmarking suite
- **🏭 Production Ready**: Proper error handling, logging, and monitoring capabilities
- **🐳 Docker Support**: Multi-stage builds with security hardening
- **🔧 Easy Configuration**: Environment variables and command-line options

## 📋 Supported Record Types

| Type | Description | Example |
|------|-------------|---------|
| **A** | IPv4 addresses | `./betterdns example.com` |
| **AAAA** | IPv6 addresses | `./betterdns -t AAAA example.com` |
| **CNAME** | Canonical names | `./betterdns -t CNAME www.example.com` |
| **NS** | Name servers | `./betterdns -t NS example.com` |
| **MX** | Mail exchange | `./betterdns -t MX example.com` |
| **TXT** | Text records | `./betterdns -t TXT example.com` |
| **SOA** | Start of authority | `./betterdns -t SOA example.com` |
| **ANY** | All available records | `./betterdns -t ANY example.com` |

> 📚 **Detailed record type documentation**: [betterdns.arghya.dev/docs/record-types](https://betterdns.arghya.dev/docs/record-types)

## 📋 Requirements

- **Compiler**: C++23 compatible (GCC 12+ or Clang 15+)
- **Build System**: CMake 3.20 or higher
- **Platform**: POSIX-compliant system (Linux, macOS, Unix)
- **Network**: Internet connection for DNS queries
- **Optional**: Docker for containerized deployment

> 🔧 **Installation guide**: [betterdns.arghya.dev/docs/installation](https://betterdns.arghya.dev/docs/installation)

## 🔨 Building

### Local Build

```bash
# Clone the repository
git clone https://github.com/uiuxarghya/betterdns.git
cd betterdns

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the project
make -j$(nproc)

# Run tests
make test

# Install (optional)
sudo make install
```

### Docker Build

```bash
# Build Docker image
docker build -t betterdns .

# Or use pre-built image
docker pull ghcr.io/uiuxarghya/betterdns:latest

# Run with Docker
docker run --rm betterdns example.com
```

> 🏗️ **Detailed build instructions**: [betterdns.arghya.dev/docs/building](https://betterdns.arghya.dev/docs/building)

## 🚀 Usage

### Basic Usage

```bash
# Resolve a domain name
./betterdns example.com

# Resolve with verbose output showing resolution path
./betterdns -v example.com

# Specify query type
./betterdns -t AAAA example.com

# Query any record type
./betterdns -t ANY example.com
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-v, --verbose` | Show detailed resolution path | false |
| `-t, --type <TYPE>` | Query type (A, AAAA, CNAME, NS, MX, TXT, SOA, ANY) | A |
| `-a, --all` | Resolve both A and AAAA records | false |
| `-T, --timeout <SEC>` | Query timeout in seconds | 5 |
| `-h, --help` | Show help message | - |
| `--version` | Show version information | - |

> 📖 **Complete usage guide**: [betterdns.arghya.dev/docs/usage](https://betterdns.arghya.dev/docs/usage)

### Examples

```bash
# Basic A record lookup
$ ./betterdns example.com
*************

# Verbose resolution showing the full path
$ ./betterdns -v example.com
Querying root server ********** for example.com
Received referral to .com TLD servers
Querying TLD server ********** for example.com
Received referral to authoritative servers
Querying authoritative server ************** for example.com
Final answer: *************

# IPv6 lookup
$ ./betterdns -t AAAA example.com
2606:2800:220:1:248:1893:25c8:1946

# Resolve both A and AAAA records
$ ./betterdns --all example.com
**************
2607:f8b0:4004:c1b::65
```

## ⚙️ Configuration

BetterDNS can be configured using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `BETTERDNS_UDP_TIMEOUT` | UDP query timeout (seconds) | 5 |
| `BETTERDNS_TCP_TIMEOUT` | TCP query timeout (seconds) | 10 |
| `BETTERDNS_MAX_CACHE_SIZE` | Maximum cache entries | 10000 |
| `BETTERDNS_MAX_RECURSION_DEPTH` | Maximum recursion depth | 16 |
| `BETTERDNS_ENABLE_IPV6` | Enable IPv6 queries (true/false) | true |
| `BETTERDNS_VERBOSE` | Enable verbose output (true/false) | false |
| `BETTERDNS_LOG_LEVEL` | Log level (debug/info/warn/error) | info |

### Example Configuration

```bash
# Set custom timeouts
export BETTERDNS_UDP_TIMEOUT=10
export BETTERDNS_TCP_TIMEOUT=20

# Increase cache size
export BETTERDNS_MAX_CACHE_SIZE=50000

# Enable verbose mode by default
export BETTERDNS_VERBOSE=true

# Run with custom configuration
./betterdns example.com
```

> 🔧 **Advanced configuration**: [betterdns.arghya.dev/docs/configuration](https://betterdns.arghya.dev/docs/configuration)

## 🏗️ Architecture

BetterDNS is designed with a modular architecture:

- **🔄 Resolver Engine**: Core recursive resolution logic
- **📦 Packet Builder/Parser**: DNS protocol implementation
- **🌐 Network Layer**: UDP/TCP communication with DNS servers
- **💾 Cache System**: Intelligent caching with TTL management
- **⚙️ Configuration**: Root servers and system configuration

> 🏛️ **Architecture deep dive**: [betterdns.arghya.dev/docs/architecture](https://betterdns.arghya.dev/docs/architecture)

## ⚡ Performance

BetterDNS is optimized for performance:

- **⚡ Sub-second resolution** for most queries
- **🧠 Intelligent caching** reduces redundant queries
- **🔄 Concurrent processing** for multiple queries
- **💾 Memory-efficient** implementation
- **📊 Benchmarking tools** included for performance analysis

> 📈 **Performance benchmarks**: [betterdns.arghya.dev/docs/performance](https://betterdns.arghya.dev/docs/performance)

## 🧪 Testing

Comprehensive testing suite included:

```bash
# Run all tests
cd build
./tests/betterdns_tests

# Run specific test suites
./tests/betterdns_tests --gtest_filter="ResolverTest.*"
./tests/betterdns_tests --gtest_filter="CacheTest.*"
./tests/betterdns_tests --gtest_filter="PacketTest.*"

# Run benchmarks
./benchmarks/betterdns_benchmark

# Run with verbose output
./tests/betterdns_tests --gtest_output=xml:test_results.xml
```

> 🔬 **Testing guide**: [betterdns.arghya.dev/docs/testing](https://betterdns.arghya.dev/docs/testing)

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes with proper tests
4. **Follow** the coding standards (use provided formatting scripts)
5. **Submit** a pull request

> 📋 **Contributing guide**: [betterdns.arghya.dev/docs/contributing](https://betterdns.arghya.dev/docs/contributing)

## 📚 Documentation & Resources

| Resource | Description | Link |
|----------|-------------|------|
| **📖 Documentation** | Complete guides and tutorials | [betterdns.arghya.dev](https://betterdns.arghya.dev) |
| **🔧 API Reference** | Detailed API documentation | [betterdns.arghya.dev/docs/api](https://betterdns.arghya.dev/docs/api) |
| **🏗️ Architecture** | System design and internals | [betterdns.arghya.dev/docs/architecture](https://betterdns.arghya.dev/docs/architecture) |
| **📈 Performance** | Benchmarks and optimization | [betterdns.arghya.dev/docs/performance](https://betterdns.arghya.dev/docs/performance) |
| **🧪 Testing** | Testing strategies and tools | [betterdns.arghya.dev/docs/testing](https://betterdns.arghya.dev/docs/testing) |
| **🤝 Contributing** | How to contribute | [betterdns.arghya.dev/docs/contributing](https://betterdns.arghya.dev/docs/contributing) |

## 📄 License

This project is licensed under the BSD 3-Clause License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Arghya Ghosh** ([@uiuxarghya](https://github.com/uiuxarghya))
- 🌐 Website: [arghya.dev](https://arghya.dev)
- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 📚 Documentation: [betterdns.arghya.dev](https://betterdns.arghya.dev)

## 🙏 Acknowledgments

- **RFC Authors** for DNS protocol specifications
- **Root Server Operators** for maintaining DNS infrastructure
- **C++ Standards Committee** for modern language features
- **Open Source Community** for tools and libraries

## 🔗 Links

- **📖 Documentation**: [betterdns.arghya.dev](https://betterdns.arghya.dev)
- **🐙 GitHub**: [github.com/uiuxarghya/betterdns](https://github.com/uiuxarghya/betterdns)
- **🐳 Docker**: [ghcr.io/uiuxarghya/betterdns](https://github.com/uiuxarghya/betterdns/pkgs/container/betterdns)
- **📋 Issues**: [github.com/uiuxarghya/betterdns/issues](https://github.com/uiuxarghya/betterdns/issues)
- **🔒 Security**: [<EMAIL>](mailto:<EMAIL>)

---

<div align="center">

**⭐ Star this project if you find it useful!**

[Documentation](https://betterdns.arghya.dev) • [Issues](https://github.com/uiuxarghya/betterdns/issues) • [Contributing](https://betterdns.arghya.dev/docs/contributing) • [License](LICENSE)

</div>
