# BetterDNS Security Policy

## Supported Versions

| Version | Supported       |
| ------- | --------------- |
| 1.0.x   | ✅ Supported     |
| < 1.0   | ❌ Not Supported |

## Reporting a Vulnerability

We take security seriously. If you discover a vulnerability, please report it **privately**:

**How to Report:**

1. **Do NOT** open a public GitHub issue.
2. Email **[<EMAIL>](mailto:<EMAIL>)**.
3. Include:

   * Description of the vulnerability
   * Steps to reproduce
   * Potential impact
   * Suggested fix (if any)

**What to Expect:**

* Acknowledgment within 48 hours
* Initial assessment within 5 business days
* Regular updates throughout the process
* Resolution of critical issues within 30 days

**Responsible Disclosure:**

* We collaborate to fix issues promptly.
* No legal action against good-faith researchers who:

  * Report responsibly
  * Avoid unauthorized data access
  * Do not disrupt services
* Credit given in advisories unless anonymity requested.

## Security Considerations

### Network Security

* DNS over HTTPS (DoH) and DNS over TLS (DoT) **planned** for future versions
* DNSSEC validation **planned**
* Queries currently sent in plaintext over UDP/TCP

### Input Validation & Memory Safety

* Validates inputs to prevent buffer overflows, format string vulnerabilities, DNS cache poisoning, and malformed packets
* Uses modern C++23 with RAII, smart pointers, bounds checking, and address sanitizer in debug builds

### Privileges

* Runs as non-privileged user
* Does not require root or privileged ports
* Compatible with restricted containers


## Security Best Practices

### For Users

* Always use the latest version
* Use BetterDNS on trusted networks
* Validate domain names before querying
* Monitor for unusual query patterns
* Configure appropriate cache limits

### For Developers

* Enforce code reviews for all changes
* Use static analysis tools (cppcheck, clang-tidy)
* Employ sanitizers and fuzz testing
* Keep dependencies up-to-date
* Follow secure coding practices

### Build Security

* Enable security-hardening compiler flags
* Consider static linking for deployment
* Strip debug symbols in release builds
* Verify build artifact checksums

---

## Known Limitations & Future Enhancements

| Limitation              | Status  | Planned Improvements             |
| ----------------------- | ------- | -------------------------------- |
| No DNSSEC validation    | Current | Full DNSSEC validation           |
| Plaintext DNS transport | Current | Encrypted transport (DoH, DoT)   |
| Basic cache protection  | Current | Enhanced cache poisoning defense |
| No rate limiting        | Current | Built-in query rate limiting     |
|                         |         | Malicious domain filtering       |


## Security Testing

* Automated: static analysis, AddressSanitizer, UBSan, Valgrind, custom fuzzing
* Manual: code review, penetration testing, network and input validation assessments


## Compliance

BetterDNS follows relevant RFCs:

* RFC 1035, RFC 2181, RFC 7766 (current)
* RFC 4034, RFC 8484 (planned)


## Security Contact

* **Email**: [<EMAIL>](mailto:<EMAIL>)
* **Response Time**: Within 48 hours

---

## Acknowledgments

Thanks to the security research community for responsible disclosures. Contributors will be credited unless anonymity is requested.

---

**Last Updated:** August 2025