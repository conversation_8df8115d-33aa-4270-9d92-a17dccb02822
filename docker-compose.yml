services:
  betterdns:
    build:
      context: .
      dockerfile: Dockerfile
    image: betterdns:latest
    container_name: betterdns
    restart: unless-stopped
    
    # Environment variables for configuration
    environment:
      - BETTERDNS_UDP_TIMEOUT=5
      - BETTERDNS_TCP_TIMEOUT=10
      - BETTERDNS_MAX_CACHE_SIZE=10000
      - BET<PERSON>RDNS_MAX_RECURSION_DEPTH=16
      - BETTERDNS_ENABLE_IPV6=true
      - BETTERDNS_VERBOSE=false
      - BETTERDNS_LOG_LEVEL=info
    
    # Network configuration
    network_mode: host
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'
    
    # Health check
    healthcheck:
      test: ["CMD", "/usr/local/bin/betterdns", "--version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Override default command for interactive use
    # Uncomment the line below to run a specific query
    # command: ["example.com"]

    # Documentation: https://betterdns.arghya.dev
    # Usage examples: https://betterdns.arghya.dev/docs/usage

  # Development service with mounted source code
  betterdns-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    image: betterdns:dev
    container_name: betterdns-dev
    volumes:
      - .:/app
      - /app/build  # Anonymous volume for build artifacts
    working_dir: /app
    command: ["bash", "-c", "cd build && make -j$(nproc) && ./tests/betterdns_tests"]
    profiles:
      - dev

  # Benchmark service
  betterdns-benchmark:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    image: betterdns:benchmark
    container_name: betterdns-benchmark
    volumes:
      - ./benchmark-results:/app/benchmark-results
    working_dir: /app/build
    command: ["./benchmarks/betterdns_benchmark", "--benchmark_out=/app/benchmark-results/results.json", "--benchmark_out_format=json"]
    profiles:
      - benchmark
