# BetterDNS Usage Guide

This guide provides comprehensive instructions for using BetterDNS, a production-ready recursive DNS resolver.

## 📚 Complete Documentation

For the most up-to-date and comprehensive documentation, visit:

**🌐 [https://betterdns.arghya.dev/docs/usage](https://betterdns.arghya.dev/docs/usage)**

This file provides a quick reference. For detailed tutorials, API reference, and advanced usage examples, please refer to the official documentation website.

## Installation

### Prerequisites

- C++23 compatible compiler (GCC 12+ or Clang 15+)
- CMake 3.20 or higher
- Internet connection for DNS queries
- POSIX-compliant system (Linux, macOS, Unix)

### Building from Source

```bash
# Clone the repository
git clone https://github.com/uiuxarghya/betterdns.git
cd betterdns

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the project
make -j$(nproc)

# Optional: Run tests
make test

# Optional: Install system-wide
sudo make install
```

### Build Options

```bash
# Debug build with sanitizers
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Release build with optimizations
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build with specific compiler
cmake .. -DCMAKE_CXX_COMPILER=clang++

# Custom installation prefix
cmake .. -DCMAKE_INSTALL_PREFIX=/usr/local
```

## Basic Usage

### Command Line Interface

```bash
# Basic domain resolution
./betterdns example.com

# Resolve with verbose output
./betterdns -v example.com

# Specify query type
./betterdns -t AAAA example.com

# Resolve both IPv4 and IPv6
./betterdns --all example.com

# Custom timeout
./betterdns -T 10 example.com
```

### Command Line Options

| Option | Long Form | Description | Default |
|--------|-----------|-------------|---------|
| `-t` | `--type` | Query type (A, AAAA, ANY) | A |
| `-v` | `--verbose` | Show detailed resolution path | false |
| `-a` | `--all` | Resolve both A and AAAA records | false |
| `-T` | `--timeout` | Query timeout in seconds | 5 |
| `-h` | `--help` | Show help message | - |
| | `--version` | Show version information | - |

### Query Types

- **A**: IPv4 addresses (default)
- **AAAA**: IPv6 addresses
- **ANY**: All available record types

## Examples

### Basic Resolution

```bash
# Resolve IPv4 address
$ ./betterdns example.com
*************

# Resolve IPv6 address
$ ./betterdns -t AAAA example.com
2606:2800:220:1:248:1893:25c8:1946

# Resolve both IPv4 and IPv6
$ ./betterdns --all example.com
**************
2607:f8b0:4004:c1b::65
```

### Verbose Resolution

```bash
$ ./betterdns -v example.com
[VERBOSE] Starting recursive resolution for example.com
[VERBOSE] Querying servers at depth 0 for example.com
[VERBOSE] Querying server: **********
[VERBOSE] Following referral to 2 servers
[VERBOSE] Querying servers at depth 1 for example.com
[VERBOSE] Querying server: **********
[VERBOSE] Following referral to 2 servers
[VERBOSE] Querying servers at depth 2 for example.com
[VERBOSE] Querying server: **************
*************

Resolution for example.com (A):
Time taken: 245 ms
From cache: no
Addresses found: 1

Resolution completed successfully.

Cache Statistics:
Total entries: 3
Hit ratio: 0%
Hits: 0, Misses: 3
```

### Error Handling

```bash
# Invalid domain
$ ./betterdns invalid..domain
Error: Invalid domain name: invalid..domain

# Non-existent domain
$ ./betterdns nonexistent-domain-12345.com
Resolution failed for nonexistent-domain-12345.com: No response from any server

# Network timeout
$ ./betterdns -T 1 slow-server.com
Resolution failed for slow-server.com: Query timeout
```

## Configuration

### Environment Variables

BetterDNS supports configuration through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `BETTERDNS_UDP_TIMEOUT` | UDP query timeout (seconds) | 5 |
| `BETTERDNS_TCP_TIMEOUT` | TCP query timeout (seconds) | 10 |
| `BETTERDNS_MAX_CACHE_SIZE` | Maximum cache entries | 10000 |
| `BETTERDNS_MAX_RECURSION_DEPTH` | Maximum recursion depth | 16 |
| `BETTERDNS_ENABLE_IPV6` | Enable IPv6 queries (true/false) | true |
| `BETTERDNS_VERBOSE` | Enable verbose output (true/false) | false |

### Example Configuration

```bash
# Set custom timeouts
export BETTERDNS_UDP_TIMEOUT=10
export BETTERDNS_TCP_TIMEOUT=20

# Increase cache size
export BETTERDNS_MAX_CACHE_SIZE=50000

# Enable verbose mode by default
export BETTERDNS_VERBOSE=true

# Run with custom configuration
./betterdns example.com
```

## Performance Tuning

### Cache Configuration

```bash
# Large cache for high-volume usage
export BETTERDNS_MAX_CACHE_SIZE=100000

# Disable cache for testing
export BETTERDNS_MAX_CACHE_SIZE=0
```

### Timeout Tuning

```bash
# Fast timeout for quick responses
export BETTERDNS_UDP_TIMEOUT=2

# Longer timeout for slow networks
export BETTERDNS_UDP_TIMEOUT=15
```

### Concurrent Usage

For multiple concurrent queries, consider using the resolver programmatically or running multiple instances:

```bash
# Parallel resolution
./betterdns example.com &
./betterdns example.org &
./betterdns example.net &
wait
```

## Integration

### Shell Scripts

```bash
#!/bin/bash
# Resolve multiple domains
domains=("example.com" "example.org" "example.net")

for domain in "${domains[@]}"; do
    echo "Resolving $domain:"
    ./betterdns "$domain"
    echo
done
```

### System Integration

```bash
# Add to PATH
sudo cp betterdns /usr/local/bin/

# Create alias
echo 'alias dns="betterdns"' >> ~/.bashrc

# Use in scripts
if betterdns example.com > /dev/null 2>&1; then
    echo "Domain is reachable"
else
    echo "Domain resolution failed"
fi
```

## Troubleshooting

### Common Issues

1. **Build Errors**
   ```bash
   # Check compiler version
   g++ --version
   clang++ --version
   
   # Ensure C++23 support
   cmake .. -DCMAKE_CXX_STANDARD=23
   ```

2. **Network Issues**
   ```bash
   # Test network connectivity
   ping *******
   
   # Check firewall settings
   sudo ufw status
   
   # Test with verbose mode
   ./betterdns -v example.com
   ```

3. **Permission Issues**
   ```bash
   # No special permissions required
   # BetterDNS uses unprivileged ports
   ```

### Debug Mode

```bash
# Build in debug mode
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Run with verbose output
./betterdns -v domain.com

# Check with strace (Linux)
strace -e network ./betterdns domain.com
```

### Performance Issues

```bash
# Run benchmarks
cd build
make run_benchmarks

# Check cache effectiveness
export BETTERDNS_VERBOSE=true
./betterdns example.com  # First run
./betterdns example.com  # Second run (should be cached)
```

## Exit Codes

| Code | Meaning |
|------|---------|
| 0 | Success |
| 1 | General error |
| 2 | Invalid arguments |
| 3 | DNS resolution failed |
| 4 | Network error |

## Best Practices

1. **Use caching** for repeated queries
2. **Set appropriate timeouts** based on network conditions
3. **Monitor cache hit ratios** in production
4. **Use verbose mode** for debugging
5. **Handle exit codes** in scripts
6. **Consider IPv6** for modern networks
7. **Test with various domains** during development

## Support

For issues, questions, or contributions:

- GitHub Issues: https://github.com/uiuxarghya/betterdns/issues
- Documentation: See `docs/` directory
- Examples: See `examples/` directory (if available)

## See Also

- [Design Document](design.md)
- [RFC References](rfc_references.md)
- [Packet Format](packet_format.md)
