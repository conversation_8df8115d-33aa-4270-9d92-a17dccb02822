#!/bin/bash

# BetterDNS Release Script
# Automates the release process with proper versioning and validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ] || [ ! -d "src" ]; then
    log_error "Please run this script from the project root directory"
    exit 1
fi

# Parse command line arguments
VERSION=""
DRY_RUN=false
SKIP_TESTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -v, --version VERSION    Set the release version (e.g., 1.0.1)"
            echo "  --dry-run               Show what would be done without making changes"
            echo "  --skip-tests            Skip running tests (not recommended)"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --version 1.0.1      Create release version 1.0.1"
            echo "  $0 --dry-run --version 1.0.1  Preview release process"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

if [ -z "$VERSION" ]; then
    log_error "Version is required. Use --version to specify it."
    exit 1
fi

# Validate version format (semantic versioning)
if ! [[ $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    log_error "Version must follow semantic versioning format (e.g., 1.0.1)"
    exit 1
fi

log_info "Starting release process for version $VERSION"

if [ "$DRY_RUN" = true ]; then
    log_warning "DRY RUN MODE - No changes will be made"
fi

# Check git status
log_info "Checking git status..."
if [ -n "$(git status --porcelain)" ]; then
    log_error "Working directory is not clean. Please commit or stash changes."
    exit 1
fi

# Check if we're on main branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "main" ]; then
    log_warning "Not on main branch (currently on $CURRENT_BRANCH)"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run tests unless skipped
if [ "$SKIP_TESTS" = false ]; then
    log_info "Running tests..."
    cd build
    if ! ./tests/betterdns_tests --gtest_brief=1; then
        log_error "Tests failed. Fix tests before releasing."
        exit 1
    fi
    cd ..
    log_success "All tests passed"
else
    log_warning "Skipping tests (not recommended for releases)"
fi

# Update version in CMakeLists.txt
log_info "Updating version in CMakeLists.txt..."
if [ "$DRY_RUN" = false ]; then
    sed -i "s/VERSION [0-9]\+\.[0-9]\+\.[0-9]\+/VERSION $VERSION/" CMakeLists.txt
fi
log_success "Version updated to $VERSION"

# Update CHANGELOG.md
log_info "Updating CHANGELOG.md..."
if [ "$DRY_RUN" = false ]; then
    # Get current date
    RELEASE_DATE=$(date +%Y-%m-%d)
    
    # Replace [Unreleased] with version and date
    sed -i "s/## \[Unreleased\]/## [$VERSION] - $RELEASE_DATE/" CHANGELOG.md
    
    # Add new Unreleased section
    sed -i "/## \[$VERSION\] - $RELEASE_DATE/i\\## [Unreleased]\\n" CHANGELOG.md
fi
log_success "CHANGELOG.md updated"

# Build release version
log_info "Building release version..."
cd build
if [ "$DRY_RUN" = false ]; then
    cmake .. -DCMAKE_BUILD_TYPE=Release
    make clean
    make -j$(nproc)
fi
cd ..
log_success "Release build completed"

# Create git tag
log_info "Creating git tag..."
if [ "$DRY_RUN" = false ]; then
    git add CMakeLists.txt CHANGELOG.md
    git commit -m "chore: bump version to $VERSION"
    git tag -a "v$VERSION" -m "Release version $VERSION"
fi
log_success "Git tag v$VERSION created"

# Generate release notes
log_info "Generating release notes..."
RELEASE_NOTES_FILE="release-notes-$VERSION.md"
if [ "$DRY_RUN" = false ]; then
    echo "# BetterDNS Release $VERSION" > "$RELEASE_NOTES_FILE"
    echo "" >> "$RELEASE_NOTES_FILE"
    echo "## Changes" >> "$RELEASE_NOTES_FILE"
    echo "" >> "$RELEASE_NOTES_FILE"
    
    # Extract changes from CHANGELOG.md
    sed -n "/## \[$VERSION\]/,/## \[/p" CHANGELOG.md | head -n -1 | tail -n +2 >> "$RELEASE_NOTES_FILE"
    
    echo "" >> "$RELEASE_NOTES_FILE"
    echo "## Installation" >> "$RELEASE_NOTES_FILE"
    echo "" >> "$RELEASE_NOTES_FILE"
    echo "\`\`\`bash" >> "$RELEASE_NOTES_FILE"
    echo "git clone https://github.com/uiuxarghya/betterdns.git" >> "$RELEASE_NOTES_FILE"
    echo "cd betterdns" >> "$RELEASE_NOTES_FILE"
    echo "git checkout v$VERSION" >> "$RELEASE_NOTES_FILE"
    echo "mkdir build && cd build" >> "$RELEASE_NOTES_FILE"
    echo "cmake .. -DCMAKE_BUILD_TYPE=Release" >> "$RELEASE_NOTES_FILE"
    echo "make -j\$(nproc)" >> "$RELEASE_NOTES_FILE"
    echo "\`\`\`" >> "$RELEASE_NOTES_FILE"
fi
log_success "Release notes generated: $RELEASE_NOTES_FILE"

# Summary
echo ""
log_success "Release $VERSION prepared successfully!"
echo ""
log_info "Next steps:"
echo "  1. Review the changes: git show HEAD"
echo "  2. Push the release: git push origin main --tags"
echo "  3. Create GitHub release using: $RELEASE_NOTES_FILE"
echo "  4. Update documentation if needed"
echo ""

if [ "$DRY_RUN" = true ]; then
    log_warning "This was a dry run. No changes were made."
fi
